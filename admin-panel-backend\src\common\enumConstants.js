export const RESPONSE_STATUS = {
  SUCCESS: 1,
  ERROR: 0,
  AUTH_FAIL: 4,
  PROXY: 17
};

export const STATUSES = {
  ACTIVE: 1,
  INACTIVE: 0,
};

export const PRIZE_TYPES = {
  REAL: "real",
  MERCHANDISE: "merchandise",
};

export const CONTEST_PRIZE_TYPES = {
  REAL: "real",
  BONUS: "bonus",
};

export const CONTEST_TYPES = {
  AUTO: "auto",
  GUARANTEED: "guaranteed",
};

export const CONTEST_PRIZE_DISTRIBUTION = {
  PERCENTAGE: "percentage",
  FIXED_VALUE: "fixed_value",
};

export const SPIN_WHEEL_RESULT = {
  WIN: "win",
  LOSS: "loss",
};

export const BANNER_TYPES = {
  APP: "app",
  PROMO: "promo",
};

export const LOBBY_BANNER_TYPES = {
  REFER_FRIEND: "refer_friend",
  DEPOSIT: "deposit",
  SIGNUP: "signup",
};

export const LOBBY_GAME_TYPES = {
  ALL: "all",
  DAILY_FANTASY: "daily_fantasy",
};

export const DISCOUNT_TYPES = {
  AMOUNT: "amount",
  PERCENTAGE: "percentage",
};

export const PROMO_CODE_TYPES = {
  BONUS: "bonus",
  REAL: "real",
};

export const CONTEST_MODE_TYPES = {
  PUBLIC: "public",
  PRIVATE: "private",
};

export const PROMO_CODE_CATEGORY_TYPES = {
  FIRST_DEPOSITE: "first_deposit",
  DEPOSIT_RANGE: "deposite_range",
  DEPOSIT: "deposite",
  CONTEST_JOIN: "contest_join",
};

export const YES_NO_TYPE = {
  YES: 1,
  NO: 0,
};

export const PAYMENT_STATUSES = {
  PENDING: "pending",
  PAID: "paid",
  FAILED: "failed",
};

export const TRANSACTION_TYPE = {
  CREDIT: "credit",
  DEBIT: "debit",
};

export const WALLET_TRANSACTION_TYPE = {
  REAL: "real",
  BONUS: "bonus",
  WINNING: "winning",
};

export const FINANCIAL_CATEGORY_TYPES = {
  INCOME: "income",
  EXPENSE: "expense",
};

export const APPROVAL_STATUS = {
  PENDING: 0,
  APPROVED: 1,
  REJECTED: 2
}

export const GENDER_TYPES = {
  MALE: "male",
  FEMALE: "female",
  OTHER: "other"
};

export const PLATFORMS = {
  ANDROID: "android",
  IOS: "ios"
}

export const SEND_STATUS = {
  PENDING: "pending",
  SUCCESS: "success",
  FAILED: "failed"
}

export const CMS_APP_KEYS = {
  HOW_TO_PLAY: "how_to_play",
  PLAYMORE: "playmore",
  WITHDRAWAL_POINTS: "withdrawl_points",
  SPIN_RULES: "spin_rules"
}

export const BONUS_TYPE = {
  REDEEM: "redeem",
  SPIN_WHEEL: "spin_wheel",
  REFERRAL: "referral",
  PROMO_CODE: "promo_code",
}

export const TRANSACTION_TYPES_TAB = {
  ALL:"all",
  CREDITS:"credit",
  WINNINGS:"winning",
  DEBIT:"debit",
  BONUS:"bonus"
}

export const ENQUIRY_TYPES = {
  APP:"app",
  WEBSITE:"website"
}

export const PLAYER_TYPES = {
  BATSMAN: "batsman",
  FIELDER: "fielder",
  BOWLER: "bowler",
  DID_NOT_BAT: "did_not_bat",
};
