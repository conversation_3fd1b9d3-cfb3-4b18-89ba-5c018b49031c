{"name": "user-mlm-react-panel", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^6.4.2", "@mui/material": "^6.4.2", "@reduxjs/toolkit": "^2.5.1", "@tanstack/react-query": "4.22", "animejs": "^3.2.2", "antd": "^5.24.1", "axios": "^1.7.9", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "i18next": "^24.2.2", "react": "^19.0.0", "react-bootstrap": "^2.10.9", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "react-i18next": "^15.4.0", "react-js-loader": "^0.1.3", "react-loading-skeleton": "^3.5.0", "react-redux": "^9.2.0", "react-router-dom": "^7.1.5", "react-scripts": "^5.0.1", "react-select": "^5.10.0", "react-toastify": "^11.0.3", "reactstrap": "^9.2.3", "redux-thunk": "^3.1.0", "remixicon": "^4.6.0", "remixicon-react": "^1.0.0", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"ajv": "^7.2.4"}}