{"aboutUs": "About Us", "accepted": "Accepted", "accountHolder": "Account Holder", "accountNumber": "Account Number", "action": "Action", "addAddress": "Add Address", "addFollowUp": "Add Follow-up", "addLead": "Add Lead", "addNewAddress": "Add New Address", "addToCart": "Add to Cart", "addGuest": "Add Guest", "addHost": "Add Host", "additionalDetails": "Additional Details", "addonComingSoon": "<PERSON>don Coming Soon", "address": "Address", "addressLine1": "Address Line 1", "addressLine2": "Address Line 2", "admin": "Admin", "adminInbox": "Admin Inbox", "all": "All", "allItemsAreSelected": "All items are selected", "amount": "Amount", "amountPayable": "Amount Payable", "anAdditionalAmountOf": "An additional amount of", "apply": "Apply", "approved": "Approved", "areYouSureToCancelSubscription": "Are you sure to cancel the subscription?", "areYouSureToCloseParty": "Are you sure to close the party?", "assignee": "Assignee", "attachment": "Attachment", "autoRenewal": "Auto Renewal", "autoPay": "autoPay", "availableAmount": "Available Amount", "availablePayoutAmount": "Available Payout Amount", "back": "Back", "balance": "Balance", "bankDetails": "Bank Details", "bankName": "Bank Name", "boardView": "Board View", "bankTransfer": "Bank Transfer", "branchName": "Branch Name", "buyNow": "Buy Now", "category": "Category", "checkout": "Checkout", "chooseAnImageFileOrDragItHere": "Choose an image file or drag it here", "chooseTheCategory": "Choose the category", "city": "City", "clearAll": "Clear All", "clientInformation": "Client Information", "close": "Close", "commission": "Commission", "complete": "Complete", "compose": "Compose", "confirmPassword": "Confirm Password", "contactUs": "Contact Us", "contactInformation": "Contact Information", "contactDetails": "Contact Details", "continue": "Continue", "count": "Count", "country": "Country", "createTicket": "Create Ticket", "createdOn": "Created On", "creditedAmount": "Credited Amount", "crm": "CRM", "crmGraph": "CRM Graph", "currency": "<PERSON><PERSON><PERSON><PERSON>", "current": "Current", "currentFollowDate": "Current Follow Date", "CurrentPassword": "Current Password", "currentRanking": "Current Ranking", "currentBalance": "Current Balance", "currentTopBanner": "Current Top Banner", "currentTransactionPassword": "Current Transaction Password", "dashboard": "Dashboard", "date": "Date", "dateOfBirth": "Date of Birth", "day": "Day", "days": "Days", "daysLeft": "Days Left", "debitedAmount": "Debited Amount", "defaultCurrency": "<PERSON><PERSON><PERSON>", "delete": "Delete", "description": "Description", "documentBothSide": "Document (Both Sides)", "documentFiles": "Document Files", "documentName": "Document Name", "downlineMembers": "Downline Members", "downloadMaterials": "Download Materials", "dribbble": "<PERSON><PERSON><PERSON>", "earnings": "Earnings", "earningsAndExpenses": "Earnings and Expenses", "edit": "Edit", "editLead": "Edit Lead", "EDITPARTY": "Edit Party", "email": "Email", "emailExists": "Email already exists", "emailAddress": "Email Address", "emailId": "Email ID", "enquireNowForMoreDetails": "Enquire now for more details", "epin": "E-Pin", "epinCount": "E-Pin Count", "epinList": "E-Pin List", "ePinPurchase": "E-Pin Purchase", "epinRequest": "E-Pin Request", "epinTransfer": "E-Pin Transfer", "epinTransferHistory": "E-Pin Transfer History", "eWallet": "E-Wallet", "e-wallet": "E-Wallet", "ewalletBalance": "E-Wallet Balance", "ewalletFundTransfer": "E-Wallet Fund Transfer", "expenses": "Expenses", "expiry": "Expiry", "expiryDate": "Expiry Date", "ewallet": "E-wallet", "facebook": "Facebook", "faqs": "FAQs", "female": "Female", "freeJoining": "Free Joining", "fileUpload": "File Upload", "fillTheForm": "Fill the Form", "findLead": "Find Lead", "finish": "Finish", "firstName": "First Name", "followUpsToday": "Follow-ups Today", "followUs": "Follow Us", "followupDate": "Follow-up Date", "formIntro": "Form Introduction", "from": "From", "freeJoinPlaceHolder": "Free Join Placeholder", "fullName": "Full Name", "gender": "Gender", "genealogyTree": "Genealogy Tree", "grandTotal": "Grand Total", "groupPV": "Group PV", "guestManagement": "Guest Management", "home": "Home", "hostManagement": "Host Management", "iAcceptTermsAndConditions": "I accept the terms and conditions", "ifscCode": "IFSC Code", "invalidFormat": "Invalid Format", "invalidEmailFormat": "Invalid Email-Id", "inbox": "Inbox", "instagram": "Instagram", "interested": "Interested", "invoice": "Invoice", "limit": "Items per Page", "joinings": "Joinings", "kyc": "KYC", "kycDetails": "KYC Details", "language": "Language", "lastUpdated": "Last Updated", "lastMonth": "Last Month", "lastName": "Last Name", "lead": "Lead", "leadAddedFromDate": "Lead Added From Date", "leadAddedToDate": "Lead Added To Date", "leadDetails": "Lead Details", "leadHistory": "Lead History", "leadStatus": "Lead Status", "leadView": "Lead View", "leadCapture": "Lead Capture", "left": "Left", "leftCarry": "Left Carry", "leg": "Leg", "level": "Level", "levelCommission": "Level Commission", "levelOfInterest": "Level of Interest", "location": "Location", "Login": "<PERSON><PERSON>", "loginInformation": "Login Information", "logout": "Logout", "mailBox": "Mail box", "mainPages": "Main Pages", "makeThisPrimary": "Make This Primary", "male": "Male", "maxAgeLimit": "Max Age Limit", "maxSize2MB": "Max size 2MB", "maxPayoutAmount": "Max Payout Amount", "message": "Message", "messageToAdmin": "Message to <PERSON>min", "memberName": "Member Name", "minLengthOf5": "Min Length of 5", "missedFollowUps": "Missed Follow-ups", "minLengthIs": "Min length is", "minPayoutAmount": "Min Payout Amount", "mobile": "Mobile", "mobileNumber": "Mobile Number", "month": "Month", "more": "More", "moreDetails": "More Details", "moreInfo": "More Info", "myEarnings": "My Earnings", "NA": "N/A", "name": "Name", "newMail": "New Mail", "newMembers": "New Members", "news": "News", "NewPassword": "New Password", "newTransactionPassword": "New Transaction Password", "networks": "Networks", "next": "Next", "nextFollowupDate": "Next Follow-up Date", "no": "No", "noAddressFound": "No address found", "noDataFound": "No data found", "noWhitespaceAllowed": "No Whitespace Allowed", "noData": "No Data", "noRankAchieved": "No rank achieved", "noImagesAvailable": "No images available", "notThatInterested": "Not that interested", "notifications": "Notifications", "notVerified": "Not Verified", "oldPlan": "Old Plan", "ongoing": "Ongoing", "other": "Other", "ourPlan": "Our Plan", "overview": "Overview", "package": "Package", "packageAmount": "Package Amount", "packageOverview": "Package Overview", "paid": "Paid", "panNumber": "PAN Number", "particulars": "Particulars", "password": "Password", "passwordNotMatch": "Password not match", "payment": "Payment", "paymentDetails": "Payment Details", "paymentType": "Payment Type", "paymentMethod": "Payment Method", "payNow": "Pay Now", "payout": "Payout", "payoutFee": "Payout Fee", "payoutMethod": "Payout Method", "payoutFeeMode": "Payout Fee Mode", "payoutOverview": "Payout Overview", "payoutRequest": "Payout Request", "paypalAccount": "PayPal Account", "profile": "Profile", "pending": "Pending", "pendingEpinRequest": "Pending E-Pin Request", "personalDetails": "Personal Details", "personalPV": "Personal PV", "percentage": "Percentage", "phoneNumber": "Phone Number", "placement": "Placement", "PleaseEnterYourAccountHolder": "Please enter your account holder", "PleaseEnterYourAccountNumber": "Please enter your account number", "pleaseFillAllRequiredFields": "Please fill all required fields", "plan": "Plan", "planAmount": "Plan Amount", "planDetails": "Plan Details", "planExpiry": "Plan Expiry", "planOverview": "Plan Overview", "planType": "Plan Type", "previous": "Previous", "previousMonth": "Previous Month", "pickYourProducts": "Pick Your Products", "price": "Price", "preferredPaymentPlaceholder": "Preferred Payment Placeholder", "purchaseDate": "Purchase Date", "purchaseWallet": "Purchase Wallet", "profileView": "Profile View", "productAndSponsor": "Product And Sponsor", "quantity": "Quantity", "rating": "Rating", "recurringPayment": "Recurring Payment", "register": "Register", "registerNow": "Register Now", "registered": "Registered", "registerEmail": "Register Email", "replicaInbox": "Replica Inbox", "related": "Related", "rememberMe": "Remember Me", "renewPlan": "Renew Plan", "repurchaseReport": "Repurchase Report", "reset": "Reset", "resetPassword": "Reset Password", "resetTransactionPassword": "Reset Transaction Password", "review": "Review", "reviewDetails": "Review Details", "requested": "Requested", "requestInProgress": "Request In Progress", "rejected": "Rejected", "RepeatPassword": "Repeat Password", "repeatTransactionPassword": "Repeat Transaction Password", "requestValidity": "Request Validity", "saveChanges": "Save Changes", "search": "Search", "searchResults": "Search Results", "selectAnItem": "Select an item", "selectDate": "Select Date", "selectStatus": "Select Status", "selectYourPlan": "Select Your Plan", "select": "Select", "selectMail": "Select Mail", "selectAll": "Select All", "send": "Send", "sent": "<PERSON><PERSON>", "sendTicket": "Send Ticket", "service": "Service", "serviceName": "Service Name", "services": "Services", "serviceCharge": "Service Charge", "servicesOffered": "Services Offered", "shopping": "Shopping", "shippingAddress": "Shipping Address", "shippingDetails": "Shipping Details", "shippingInformation": "Shipping Information", "signIn": "Sign In", "signOut": "Sign Out", "siteMap": "Site Map", "slip": "Slip", "slipDetail": "<PERSON><PERSON>", "slug": "Slug", "social": "Social", "socialMedia": "Social Media", "sorryNoDataFound": "Sorry No Data Found", "status": "Status", "state": "State", "step": "Step ", "submit": "Submit", "success": "Success", "successfullyRegistered": "Successfully Registered", "sponsor": "Sponsor", "statement": "Statement", "tax": "Tax", "teamPerformance": "Team Performance", "termsAndConditions": "Terms and Conditions", "thisFieldIsRequired": "This field is required", "ticket": "Ticket", "ticketId": "Ticket ID", "ticketStatus": "Ticket Status", "tickets": "Tickets", "topEarners": "Top Earners", "totalAmount": "Total Amount", "totalCredit": "Total Credit", "totalDebit": "Total Debit", "topRecruiters": "Top Recruiters", "totalDownlineMembers": "Total Downline Members", "totalLevels": "Total Levels", "totalPaid": "Total Paid", "totalEpinAmount": "Total E-pin Amount", "tools": "Tools", "transactionPassword": "Transaction Password", "transactionHistory": "Transaction History", "transactionSuccessful": "Transaction Successful", "transferToUsername": "Transfer To Username", "transactionDate": "Transaction Date", "treeView": "Tree View", "transferHistory": "Tranfer History", "update": "Update", "updatePayout": "Update Payout", "updateProfile": "Update Profile", "upload": "Upload", "uploadImages": "Upload Images", "uploading": "Uploading", "user": "User", "userProfile": "User Profile", "username": "Username", "validEpinData": "Valid E-pin Data", "verificationCode": "Verification Code", "verify": "Verify", "view": "View", "viewLead": "View Lead", "viewMembers": "View Members", "weKnowYou'llLoveIt": "We know you'll love it!", "welcome": "Welcome", "withdraw": "Withdraw", "withdrawal": "<PERSON><PERSON><PERSON>", "withdrawalAmount": "<PERSON><PERSON><PERSON> Amount", "withdrawalDetails": "<PERSON><PERSON><PERSON>", "yes": "Yes", "year": "Year", "youAre": "You are", "yourBankDetails": "Your Bank Details", "yourMessage": "Your Message", "yourPassword": "Your Password", "yourToken": "Your Token", "yourTransactionPassword": "Your Transaction Password", "zipCode": "Zip Code", "bonus": "Bonus", "referralReward": "Referral", "leadershipBonus": "Leadership Bonus", "generalInfo": "General Info", "socialMediaLinks": "Social Media Links", "appLink": "App Link", "appVersion": "App Version", "systemEmail": "System Email", "generalSettings": "General Settings", "manageGeneralSettings": "Manage General Settings", "staff": "Staff", "general-settings": "General Settings", "manageStaff": "Manage Staff", "addStaff": "Add Staff", "editStaff": "Edit Staff", "viewStaff": "View Staff", "phone": "Phone", "role": "Role", "manageRole": "Manage Role", "addRole": "Add Role", "editRole": "Edit Role", "viewRole": "View Role", "roleName": "Role Name", "updatePassword": "Update Password", "originalPassword": "Original Password"}