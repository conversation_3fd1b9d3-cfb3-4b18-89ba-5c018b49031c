version: 0.0
os: linux
files:
  - source: ./
    destination: /home/<USER>/admin-panel-backend
    overwrite: true
file_exists_behavior: OVERWRITE
hooks:
  AfterInstall:
    - location: scripts/install_dependencies.sh
      timeout: 300
  ApplicationStart:
    - location: scripts/start_server.sh
      timeout: 300
branch_config:
    wip\/.*: ~ 
    main:
        deploymentGroupName: MLM-DG
        deploymentGroupConfig:
            serviceRoleArn: arn:aws:iam::257153283105:role/autoScalingMLMRole