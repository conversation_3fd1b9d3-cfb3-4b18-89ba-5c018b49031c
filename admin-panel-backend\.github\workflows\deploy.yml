name: Node.js CI/CD to AWS Auto Scaling

on:
  push:
    branches: [ "main" ]  # Trigger on main branch push

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'  # Use your Node.js version

      - name: Install dependencies
        run: npm install

      - name: Zip deployment package
        run: |
          zip -r deploy.zip . \
            -x '*.git*' \
            -x '.github/*' \
            -x '.env*'  # Exclude sensitive files

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ap-south-1  # Change to your region

      - name : Create Code Deploy
        id: deploy
        uses: webfactory/create-aws-codedeploy-deployment@v0.2.2
        with:
            application: MLMCodeDeploy