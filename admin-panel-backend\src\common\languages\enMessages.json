{"ALL_VALUES_REQUIRED": "All * fields are required!", "INVALID_OBJECT_ENUM": "Invalid keys passed!", "INVALID_ENUM": "Invalid Enum Passed!", "DUPLICATE_OBJECT_ENUM": "Duplicate keys passed!", "PASSWORD_MISMATCH": "Both password and confirm password should be matched!", "INVALID_CREDENTIALS": "Invalid credentials. Please check email address or password and try again.", "ADMIN_LOGGED_IN_SUCCESSFULLY": "<PERSON><PERSON> logged in successfully", "INVALID_OBJECT_ID": "Invalid collection object id provided!", "PASSWORD_UPDATED_SUCCESSFULLY": "Password updated successfully", "INVALID_PASSWORD": "Invalid password. Please check your original password and try again!", "SAME_PASSWORD": "Original password and new password are same!", "AUTH_TOKEN_REQUIRED": "Authentication Token is required!", "AUTH_FAILED": "Authentication Failed!", "INVALID_AUTH_TOKEN": "Authentication token is Invalid or expired", "EMAIL_SENT": "<PERSON>ail sent successfully", "PASSWORD_RESET": "Password has been reset successfully!", "OTP_INVALID": "otp Code is invalid or expired please try again", "ADMIN_FETCHED_SUCCESSFULLY": "<PERSON><PERSON> fetched successfully", "TRY_AGAIN_LATER": "There was issue with request please try again later!", "UNAUTHORIZED_ADMIN": "You are not allowed to log in please contact your admin", "ID_AND_STATUS_REQUIRED": "Id and status field required", "ID_AND_IS_PUBLISH_REQUIRED": "Id and isPublish field required", "EMAIL_AND_PASSWORD_REQUIRED": "Email and password fields are required", "ID_REQUIRED": "Id required", "EMAIL_REQUIRED": "Email required", "EMAIL_SENDING_ERROR": "Unable to send email please try again later", "DATA_ADDED": "Data Added Successfully", "DATA_UPDATED": "Data Updated Successfully", "DATA_FETCHED": "Data Fetched Successfully", "NO_DATA": "No Data Found", "STATUS_UPDATED": "Status Updated Successfully", "DATA_DELETED": "Data Deleted Successfully", "DATA_DELETE_ERROR": "Error while deleting the data, Please try again", "DATA_ADD_ERROR": "Error while adding the data, Please try again", "DATA_UPDATE_ERROR": "Error while updating the data, Please try again", "INVALID_STATUS": "Invalid status passed", "INVALID_FIELD": "Invalid {field1} Passed", "INVALID_PRIZE_TYPE": "Invalid prize type passed", "INVALID_BANNER_TYPE": "Invalid type passed it must be app or lobby", "INVALID_DISCOUNT_TYPE": "Invalid discount type passed", "INVALID_MODE_TYPE": "Invalid mode type passed", "INVALID_PROMOCODE_TYPE": "Invalid promocode type passed", "INVALID_CONTEST_MODE": "Invalid contest mode passed", "INVALID_PROMOCODE_CATEGORY": "Invalid promocode category passed", "INVALID_SPIN_WHEEL_RESULT": "Invalid spin wheel result passed", "INVALID_TYPE": "Invalid type passed", "INVALID_PAYMENT_STATUS": "Invalid payment status passed", "INVALID_CATEGORY_TYPE": "Invalid category type passed", "INVALID_WALLET_TRANS_TYPE": "Invalid wallet transaction type passed", "CONFLICT_DATA": "Data already exist", "SOMETHING_WENT_WRONG": "Something went wrong", "OTP_VERIFIED": "Otp verified successfully", "EMAIL_AND_OTP_REQUIRED": "Email and otp code is required", "INVALID_RECORD": "{field1} does not exist.", "VALUE_ALREADY_EXIST": "{field1} already exist.", "INVALID_PASSWORD_VALIDATION": "{field1} must contain 8 characters, at least one uppercase letter, at least one lowercase letter, at least one digit", "INVALID_LOBBY_BANNER_TYPE": "Invalid Banner type passed", "INVALID_LOBBY_GAME_TYPE": "Invalid game type passed", "VALUE_REQUIRED": "Value for {field1} is required", "LANG_INPUT_REQUIRED": "Language content for english or hindi not passed.", "DESCRIPTION_LANG_INPUT_REQUIRED": "Description Language content for english or hindi not passed.", "INVALID_MAX_PLAYERS": "Max player should not be greater than 8.", "INVALID_MIN_PLAYERS": "Min player should not be less than 1.", "NOT_GREATER_THAN_MAX_PLAYERS": "Min player should not be greater than Max player.", "KEY_REQUIRED": "Key is required", "UPDATE_VALUE_REQUIRED": "Value is required", "TYPE_REQUIRED": "Type is required", "KEY_VAlUE_REQUIRED": "key and value required", "ALLOW_FIELD": "Upload not allowed", "ACCESS_DENIED": "Access Denied. Contact your Admin.", "INVALID_PERCENTAGE": "Percentage should not be greater than 100.", "IMAGE_UPLOAD_FAILED": "File size exceeds the limit.", "CONTEST_MAPPED": "Contest mapped successfully!", "CONTEST_MAP_ERROR": "Error while mapping contest please try again later", "INVALID_RANK_MIN_MAX": "RankMin should be one greater than the previous RankMax", "DATA_REQUIRED": "Data is required!", "MAIL_KEY_REQUIRED": "Mail key required", "CANNOT_DELETE": "Cannot delete this record as user has already joined the match", "PIN_UPDATED": "{field1} successfully!", "INVALID_TOTAL_AMOUNT": "Total amount cannot be greater than prize pool.", "INVALID_PARTICIPANTS": "Min participants cannot be greater than Max participants.", "INVALID_PRIZE_POOL": "Actual Prize Pool should be {amount}.", "RESET_PASSWORD_MODEL": "Please reset your password!", "CONTEST_MAX_ENTRIES_ALLOWED": "Maximum No Of Entries allowed {field1} for current contest", "DATA_SENT": "{field1} sent successfully", "INSUFFICIENT_BALANCE": "User balance is low.", "INVALID_PROBABILITY": "You have reached max probability balanced {balance}", "BULK_ADDED": "Added new {player} {teams}.", "NO_ADD_OBJECT": "No new {entity} to add.", "EMPTY_ARRAY": "{field1} cannot be empty", "LENGTH_LIMIT_EXCEEDED": "Length of {field1} should not be greater than {length}", "MIN_PERCENTAGE_LIMIT_EXCEEDED": "Percentage should not be less than 0", "PERCENTAGE_LIMIT_EXCEEDED": "Percentage should not be greater than 100", "SUBSCRIPTION_ENDED": "Your subscription has expired. Please renew.", "MUST_GREATER_THEN_MAX_TEAM_COUNT": "The total sum of max players of all player positions must be greater or equal to {field1}", "MATCH_ABONDONED": "Match was Cancelled", "KYC_NOT_VERIFIED": "User kyc is not completed!", "ENQUIRY_SAVED": "Enquiry saved successfully", "FUTURE_DATE_NOT_ALLOWED": "Future date is not allowed", "PAYOUT_LEADERBOARD_ACTIVE": "Payout leaderboard is active"}