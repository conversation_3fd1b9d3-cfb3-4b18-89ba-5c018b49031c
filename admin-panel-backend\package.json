{"name": "admin-panel-backend", "version": "1.0.0", "description": "", "main": "index.js", "type": "module", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node src/index.js", "dev": "nodemon src/index.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@aws-sdk/client-s3": "^3.744.0", "axios": "^1.7.9", "bcrypt": "^5.1.1", "cli-color": "^2.0.4", "cors": "^2.8.5", "crypto-js": "^4.2.0", "crypto-random-string": "^5.0.0", "dotenv": "^16.4.7", "express": "^4.21.2", "jsonwebtoken": "^9.0.2", "moment": "^2.30.1", "mongoose": "^8.10.0", "uuid": "^11.0.5", "winston": "^3.17.0", "winston-sentry": "^0.2.1"}, "devDependencies": {"nodemon": "^3.1.9"}}